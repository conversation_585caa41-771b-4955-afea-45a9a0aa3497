package auth

import (
	"time"
)

type PasswordResetPageData struct {
	UserEmail string
	ExpiresAt time.Time
	ExpiresIn time.Duration
}

templ PasswordResetPage(data PasswordResetPageData) {
	<!DOCTYPE html>
	<html>
		<head>
			<title>Reset Your Password</title>
			<style>
				body {
					font-family: Arial, sans-serif;
					margin: 40px;
					background-color: #f5f5f5;
				}
				.container {
					max-width: 500px;
					margin: 0 auto;
					background: white;
					padding: 40px;
					border-radius: 8px;
					box-shadow: 0 2px 10px rgba(0,0,0,0.1);
				}
				h1 {
					color: #333;
					margin-bottom: 20px;
					text-align: center;
				}
				.form-group {
					margin-bottom: 20px;
				}
				label {
					display: block;
					margin-bottom: 5px;
					color: #555;
					font-weight: bold;
				}
				input[type="password"] {
					width: 100%;
					padding: 12px;
					border: 1px solid #ddd;
					border-radius: 4px;
					font-size: 16px;
					box-sizing: border-box;
				}
				button {
					width: 100%;
					padding: 12px;
					background-color: #1976d2;
					color: white;
					border: none;
					border-radius: 4px;
					font-size: 16px;
					cursor: pointer;
				}
				button:hover {
					background-color: #1565c0;
				}
				.info {
					background-color: #e3f2fd;
					padding: 15px;
					border-radius: 4px;
					margin-bottom: 20px;
					color: #1976d2;
				}
				.requirements {
					font-size: 14px;
					color: #666;
					margin-top: 5px;
				}
				.expires {
					font-size: 12px;
					color: #999;
					text-align: center;
					margin-top: 20px;
				}
			</style>
		</head>
		<body>
			<div class="container">
				<h1>Reset Your Password</h1>
				<div class="info">
					<strong>Account:</strong> { data.UserEmail }
				</div>
				<form id="resetForm" method="POST">
					<div class="form-group">
						<label for="new_password">New Password:</label>
						<input type="password" id="new_password" name="new_password" required/>
						<div class="requirements">Password must be at least 11 characters long</div>
					</div>
					<button type="submit">Reset Password</button>
				</form>
				<div class="expires">
					This reset link expires at { data.ExpiresAt.Format("2006-01-02 15:04:05 UTC") }
				</div>
			</div>
			<script>
			  (() => {
					const expiresElement = document.getElementById('expires-time');
					const utcTime = expiresElement.getAttribute('data-utc-time');
					if (utcTime) {
						const localTime = new Date(utcTime).toLocaleString();
						expiresElement.textContent = localTime;
					}
				})();

				document.getElementById('resetForm').addEventListener('submit', async function(e) {
					e.preventDefault();
					
					const password = document.getElementById('new_password').value;
					const button = document.querySelector('button');
					
					if (password.length < 11) {
						alert('Password must be at least 11 characters long');
						return;
					}
					
					button.disabled = true;
					button.textContent = 'Resetting...';
					
					try {
						const response = await fetch(window.location.pathname, {
							method: 'POST',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify({
								new_password: password
							})
						});
						
						if (response.ok) {
							document.body.innerHTML = '<div class="container"><h1 style="color: #4caf50;">Password Reset Successful</h1><p>Your password has been successfully reset. You can now log in with your new password.</p></div>';
						} else {
							const error = await response.json();
							alert('Error: ' + (error.message || 'Failed to reset password'));
							button.disabled = false;
							button.textContent = 'Reset Password';
						}
					} catch (error) {
						alert('Error: Failed to reset password. Please try again.');
						button.disabled = false;
						button.textContent = 'Reset Password';
					}
				});
			</script>
		</body>
	</html>
}
