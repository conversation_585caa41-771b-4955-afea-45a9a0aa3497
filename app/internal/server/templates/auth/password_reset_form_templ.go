// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.943
package auth

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import (
	"time"
)

type PasswordResetPageData struct {
	UserEmail string
	ExpiresAt time.Time
	ExpiresIn time.Duration
}

func PasswordResetPage(data PasswordResetPageData) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, "<!doctype html><html><head><title>Reset Your Password</title><style>\n\t\t\t\tbody {\n\t\t\t\t\tfont-family: Arial, sans-serif;\n\t\t\t\t\tmargin: 40px;\n\t\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\t}\n\t\t\t\t.container {\n\t\t\t\t\tmax-width: 500px;\n\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t\tbackground: white;\n\t\t\t\t\tpadding: 40px;\n\t\t\t\t\tborder-radius: 8px;\n\t\t\t\t\tbox-shadow: 0 2px 10px rgba(0,0,0,0.1);\n\t\t\t\t}\n\t\t\t\th1 {\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tmargin-bottom: 20px;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t\t.form-group {\n\t\t\t\t\tmargin-bottom: 20px;\n\t\t\t\t}\n\t\t\t\tlabel {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin-bottom: 5px;\n\t\t\t\t\tcolor: #555;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\t\t\t\tinput[type=\"password\"] {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tpadding: 12px;\n\t\t\t\t\tborder: 1px solid #ddd;\n\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t}\n\t\t\t\tbutton {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tpadding: 12px;\n\t\t\t\t\tbackground-color: #1976d2;\n\t\t\t\t\tcolor: white;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\tcursor: pointer;\n\t\t\t\t}\n\t\t\t\tbutton:hover {\n\t\t\t\t\tbackground-color: #1565c0;\n\t\t\t\t}\n\t\t\t\t.info {\n\t\t\t\t\tbackground-color: #e3f2fd;\n\t\t\t\t\tpadding: 15px;\n\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\tmargin-bottom: 20px;\n\t\t\t\t\tcolor: #1976d2;\n\t\t\t\t}\n\t\t\t\t.requirements {\n\t\t\t\t\tfont-size: 14px;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tmargin-top: 5px;\n\t\t\t\t}\n\t\t\t\t.expires {\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tmargin-top: 20px;\n\t\t\t\t}\n\t\t\t</style></head><body><div class=\"container\"><h1>Reset Your Password</h1><div class=\"info\"><strong>Account:</strong> ")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var2 string
		templ_7745c5c3_Var2, templ_7745c5c3_Err = templ.JoinStringErrs(data.UserEmail)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/server/templates/auth/password_reset_form.templ`, Line: 91, Col: 47}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var2))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 2, "</div><form id=\"resetForm\" method=\"POST\"><div class=\"form-group\"><label for=\"new_password\">New Password:</label> <input type=\"password\" id=\"new_password\" name=\"new_password\" required><div class=\"requirements\">Password must be at least 11 characters long</div></div><button type=\"submit\">Reset Password</button></form><div class=\"expires\">This reset link expires at ")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var3 string
		templ_7745c5c3_Var3, templ_7745c5c3_Err = templ.JoinStringErrs(data.ExpiresAt.Format("2006-01-02 15:04:05 UTC"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/server/templates/auth/password_reset_form.templ`, Line: 102, Col: 82}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var3))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 3, " This reset link expires in ")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var4 string
		templ_7745c5c3_Var4, templ_7745c5c3_Err = templ.JoinStringErrs(data.ExpiresIn.String())
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/server/templates/auth/password_reset_form.templ`, Line: 103, Col: 57}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var4))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 4, " beep</div></div><script>\n\t\t\t  (() => {\n\t\t\t\t\tconst expiresElement = document.getElementById('expires-time');\n\t\t\t\t\tconst utcTime = expiresElement.getAttribute('data-utc-time');\n\t\t\t\t\tif (utcTime) {\n\t\t\t\t\t\tconst localTime = new Date(utcTime).toLocaleString();\n\t\t\t\t\t\texpiresElement.textContent = localTime;\n\t\t\t\t\t}\n\t\t\t\t})();\n\n\t\t\t\tdocument.getElementById('resetForm').addEventListener('submit', async function(e) {\n\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\n\t\t\t\t\tconst password = document.getElementById('new_password').value;\n\t\t\t\t\tconst button = document.querySelector('button');\n\t\t\t\t\t\n\t\t\t\t\tif (password.length < 11) {\n\t\t\t\t\t\talert('Password must be at least 11 characters long');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tbutton.disabled = true;\n\t\t\t\t\tbutton.textContent = 'Resetting...';\n\t\t\t\t\t\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst response = await fetch(window.location.pathname, {\n\t\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\t\theaders: {\n\t\t\t\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tbody: JSON.stringify({\n\t\t\t\t\t\t\t\tnew_password: password\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (response.ok) {\n\t\t\t\t\t\t\tdocument.body.innerHTML = '<div class=\"container\"><h1 style=\"color: #4caf50;\">Password Reset Successful</h1><p>Your password has been successfully reset. You can now log in with your new password.</p></div>';\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst error = await response.json();\n\t\t\t\t\t\t\talert('Error: ' + (error.message || 'Failed to reset password'));\n\t\t\t\t\t\t\tbutton.disabled = false;\n\t\t\t\t\t\t\tbutton.textContent = 'Reset Password';\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\talert('Error: Failed to reset password. Please try again.');\n\t\t\t\t\t\tbutton.disabled = false;\n\t\t\t\t\t\tbutton.textContent = 'Reset Password';\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t</script></body></html>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
